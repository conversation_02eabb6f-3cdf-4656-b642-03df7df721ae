#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战地6优化工具
作者: 智宇 | QQ: 571567759 | 抖音: 35071658
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import shutil
import sys
import threading
import time
from pathlib import Path

class Battlefield6Optimizer:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("战地6优化工具 - 智宇制作")
        self.root.geometry("650x550")
        self.root.resizable(False, False)

        # 设置窗口背景色
        self.root.configure(bg="#f0f0f0")

        # 设置窗口居中
        self.center_window()

        # 设置窗口图标（如果有的话）
        try:
            if os.path.exists("头像1.png"):
                self.root.iconbitmap(default="头像1.png")
        except:
            pass

        # 配置样式
        self.setup_styles()
            
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()

        # 设置主题
        style.theme_use('clam')

        # 自定义样式
        style.configure('Title.TLabel', font=('微软雅黑', 20, 'bold'), foreground='#2c3e50')
        style.configure('Subtitle.TLabel', font=('微软雅黑', 10), foreground='#7f8c8d')
        style.configure('Info.TLabel', font=('微软雅黑', 9), foreground='#34495e')
        style.configure('Success.TLabel', font=('微软雅黑', 8), foreground='#27ae60')
        style.configure('Warning.TLabel', font=('微软雅黑', 8), foreground='#e67e22')
        style.configure('Error.TLabel', font=('微软雅黑', 8), foreground='#e74c3c')

        # 按钮样式
        style.configure('Action.TButton', font=('微软雅黑', 10, 'bold'))
        style.map('Action.TButton',
                 background=[('active', '#3498db'), ('pressed', '#2980b9')])

    def setup_variables(self):
        """初始化变量"""
        self.game_path = tk.StringVar()
        self.selected_config = tk.StringVar(value="user1")  # 默认选择通用优化
        self.is_processing = False
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(pady=20)
        
        title_label = ttk.Label(
            title_frame,
            text="战地6优化工具",
            style="Title.TLabel"
        )
        title_label.pack()

        subtitle_label = ttk.Label(
            title_frame,
            text="提升游戏性能，优化游戏体验",
            style="Subtitle.TLabel"
        )
        subtitle_label.pack(pady=(5, 0))
        
        # 作者信息框
        author_frame = ttk.LabelFrame(self.root, text="作者信息", padding=10)
        author_frame.pack(pady=10, padx=20, fill="x")
        
        author_info = [
            "作者: 智宇",
            "QQ: 571567759", 
            "抖音: 35071658"
        ]
        
        for info in author_info:
            info_label = ttk.Label(author_frame, text=info, style="Info.TLabel")
            info_label.pack(anchor="w")
            
        # 游戏路径选择框
        path_frame = ttk.LabelFrame(self.root, text="游戏路径设置", padding=10)
        path_frame.pack(pady=10, padx=20, fill="x")
        
        path_entry_frame = ttk.Frame(path_frame)
        path_entry_frame.pack(fill="x")
        
        self.path_entry = ttk.Entry(
            path_entry_frame, 
            textvariable=self.game_path,
            font=("微软雅黑", 9),
            state="readonly"
        )
        self.path_entry.pack(side="left", fill="x", expand=True)
        
        browse_btn = ttk.Button(
            path_entry_frame,
            text="浏览",
            command=self.browse_game_path,
            width=8
        )
        browse_btn.pack(side="right", padx=(10, 0))
        
        # 优化选项框
        config_frame = ttk.LabelFrame(self.root, text="优化配置选择", padding=10)
        config_frame.pack(pady=10, padx=20, fill="x")
        
        # 通用优化选项
        config1_radio = ttk.Radiobutton(
            config_frame,
            text="通用优化 (user1.cfg)",
            variable=self.selected_config,
            value="user1"
        )
        config1_radio.pack(anchor="w", pady=2)
        
        config1_desc = ttk.Label(
            config_frame,
            text="• 适合大多数配置的电脑，平衡性能与画质",
            font=("微软雅黑", 8),
            foreground="gray"
        )
        config1_desc.pack(anchor="w", padx=(20, 0))
        
        # 极致优化选项
        config2_radio = ttk.Radiobutton(
            config_frame,
            text="极致优化 (user2.cfg)",
            variable=self.selected_config,
            value="user2"
        )
        config2_radio.pack(anchor="w", pady=(10, 2))
        
        config2_desc = ttk.Label(
            config_frame,
            text="• 最大化性能提升，适合低配置电脑或追求高帧率",
            font=("微软雅黑", 8),
            foreground="gray"
        )
        config2_desc.pack(anchor="w", padx=(20, 0))
        
        # 操作按钮框
        button_frame = ttk.Frame(self.root)
        button_frame.pack(pady=20)

        self.apply_btn = ttk.Button(
            button_frame,
            text="应用优化",
            command=self.apply_optimization_threaded,
            style="Action.TButton"
        )
        self.apply_btn.pack(side="left", padx=10)

        self.reset_btn = ttk.Button(
            button_frame,
            text="重置配置",
            command=self.reset_config_threaded
        )
        self.reset_btn.pack(side="left", padx=10)

        help_btn = ttk.Button(
            button_frame,
            text="帮助",
            command=self.show_help
        )
        help_btn.pack(side="left", padx=10)

        about_btn = ttk.Button(
            button_frame,
            text="关于",
            command=self.show_about
        )
        about_btn.pack(side="left", padx=10)

        # 进度条
        self.progress_frame = ttk.Frame(self.root)
        self.progress_frame.pack(pady=10, padx=20, fill="x")

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        self.progress_bar.pack(fill="x")
        self.progress_frame.pack_forget()  # 初始隐藏

        # 状态栏
        self.status_var = tk.StringVar(value="请选择游戏路径并选择优化配置")
        self.status_label = ttk.Label(
            self.root,
            textvariable=self.status_var,
            style="Info.TLabel"
        )
        self.status_label.pack(side="bottom", pady=10)
        
    def browse_game_path(self):
        """浏览游戏路径"""
        initial_dir = "C:/Program Files (x86)/Origin Games" if os.path.exists("C:/Program Files (x86)/Origin Games") else "C:/"

        folder_path = filedialog.askdirectory(
            title="选择战地6游戏安装目录",
            initialdir=initial_dir
        )

        if folder_path:
            # 验证是否为有效的战地6目录
            if self.validate_game_path(folder_path):
                self.game_path.set(folder_path)
                self.status_var.set(f"已选择游戏路径: {folder_path}")
            else:
                messagebox.showwarning(
                    "路径无效",
                    "所选目录不是有效的战地6安装目录！\n请选择包含 bf2042.exe 的目录。"
                )

    def validate_game_path(self, path):
        """验证游戏路径是否有效"""
        if not path or not os.path.exists(path):
            return False

        # 检查是否存在战地6的可执行文件
        exe_files = ["bf2042.exe", "Battlefield 2042.exe", "BF2042.exe"]
        for exe in exe_files:
            if os.path.exists(os.path.join(path, exe)):
                return True

        # 如果没有找到exe文件，检查是否在子目录中
        for root, dirs, files in os.walk(path):
            for exe in exe_files:
                if exe in files:
                    return True
            # 只检查一级子目录，避免搜索太深
            if root != path:
                break

        return False
        
    def apply_optimization_threaded(self):
        """线程化的应用优化配置"""
        if self.is_processing:
            return

        if not self.game_path.get():
            messagebox.showwarning("警告", "请先选择游戏安装路径！")
            return

        if not self.validate_game_path(self.game_path.get()):
            messagebox.showerror("错误", "所选路径不是有效的战地6安装目录！")
            return

        # 在新线程中执行
        thread = threading.Thread(target=self.apply_optimization)
        thread.daemon = True
        thread.start()

    def apply_optimization(self):
        """应用优化配置"""
        self.set_processing_state(True)
        self.show_progress("开始应用优化配置...")

        try:
            self.update_progress(10, "检查配置文件...")
            time.sleep(0.5)  # 让用户看到进度
            # 确定源文件和目标路径
            source_file = f"{self.selected_config.get()}.cfg"
            if not os.path.exists(source_file):
                self.root.after(0, lambda: messagebox.showerror("错误", f"配置文件 {source_file} 不存在！"))
                return

            self.update_progress(30, "获取目标路径...")
            time.sleep(0.3)

            # 战地6配置文件通常在游戏目录或用户文档目录
            target_paths = self.get_config_paths()

            success_count = 0
            total_paths = len(target_paths)

            for i, target_path in enumerate(target_paths):
                progress = 30 + (i + 1) * 50 // total_paths
                self.update_progress(progress, f"处理路径 {i+1}/{total_paths}...")
                time.sleep(0.2)

                try:
                    # 确保目标目录存在
                    os.makedirs(target_path, exist_ok=True)

                    # 复制配置文件
                    target_file = os.path.join(target_path, "user.cfg")

                    # 备份原有配置（如果存在）
                    if os.path.exists(target_file):
                        backup_file = os.path.join(target_path, "user.cfg.backup")
                        shutil.copy2(target_file, backup_file)

                    # 复制新配置
                    shutil.copy2(source_file, target_file)
                    success_count += 1

                except Exception as e:
                    print(f"复制到 {target_path} 失败: {e}")
                    continue

            self.update_progress(90, "完成配置应用...")
            time.sleep(0.3)

            if success_count > 0:
                config_name = "通用优化" if self.selected_config.get() == "user1" else "极致优化"
                self.update_progress(100, f"{config_name}配置应用成功！")
                self.root.after(0, lambda: messagebox.showinfo(
                    "成功",
                    f"{config_name}配置已成功应用！\n"
                    f"已复制到 {success_count} 个位置。\n"
                    "原配置文件已备份为 user.cfg.backup"
                ))
                self.root.after(0, lambda: self.status_var.set(f"{config_name}配置应用成功"))
            else:
                self.root.after(0, lambda: messagebox.showerror("错误", "配置文件复制失败！请检查权限或路径。"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"应用配置时发生错误: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.set_processing_state(False))
            time.sleep(1)
            self.root.after(0, self.hide_progress)

    def get_config_paths(self):
        """获取可能的配置文件路径"""
        paths = []
        game_path = self.game_path.get()

        # 游戏安装目录
        paths.append(game_path)

        # 用户文档目录中的战地6配置
        documents = os.path.expanduser("~/Documents")
        bf_docs_paths = [
            os.path.join(documents, "Battlefield 2042"),
            os.path.join(documents, "Battlefield 2042", "settings"),
            os.path.join(documents, "EA Games", "Battlefield 2042"),
            os.path.join(documents, "EA Games", "Battlefield 2042", "settings")
        ]

        for path in bf_docs_paths:
            if os.path.exists(path) or True:  # 即使不存在也尝试创建
                paths.append(path)

        return paths

    def reset_config_threaded(self):
        """线程化的重置配置"""
        if self.is_processing:
            return

        if not self.game_path.get():
            messagebox.showwarning("警告", "请先选择游戏安装路径！")
            return

        result = messagebox.askyesno(
            "确认重置",
            "确定要重置配置吗？\n这将删除当前的 user.cfg 文件并恢复备份（如果存在）。"
        )

        if result:
            thread = threading.Thread(target=self.reset_config)
            thread.daemon = True
            thread.start()

    def reset_config(self):
        """重置配置"""
        self.set_processing_state(True)
        self.show_progress("开始重置配置...")

        try:
            self.update_progress(20, "获取配置路径...")
            time.sleep(0.3)

            target_paths = self.get_config_paths()
            reset_count = 0
            total_paths = len(target_paths)

            for i, target_path in enumerate(target_paths):
                progress = 20 + (i + 1) * 60 // total_paths
                self.update_progress(progress, f"处理路径 {i+1}/{total_paths}...")
                time.sleep(0.2)

                user_cfg = os.path.join(target_path, "user.cfg")
                backup_cfg = os.path.join(target_path, "user.cfg.backup")

                if os.path.exists(user_cfg):
                    os.remove(user_cfg)
                    reset_count += 1

                if os.path.exists(backup_cfg):
                    shutil.move(backup_cfg, user_cfg)

            self.update_progress(90, "完成配置重置...")
            time.sleep(0.3)

            if reset_count > 0:
                self.update_progress(100, "配置重置成功！")
                self.root.after(0, lambda: messagebox.showinfo("成功", f"配置已重置！已处理 {reset_count} 个文件。"))
                self.root.after(0, lambda: self.status_var.set("配置已重置"))
            else:
                self.root.after(0, lambda: messagebox.showinfo("提示", "没有找到需要重置的配置文件。"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"重置配置时发生错误: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.set_processing_state(False))
            time.sleep(1)
            self.root.after(0, self.hide_progress)

    def set_processing_state(self, processing):
        """设置处理状态"""
        self.is_processing = processing
        state = "disabled" if processing else "normal"
        self.apply_btn.configure(state=state)
        self.reset_btn.configure(state=state)

    def show_progress(self, message=""):
        """显示进度条"""
        self.progress_frame.pack(pady=10, padx=20, fill="x", before=self.status_label)
        self.progress_var.set(0)
        if message:
            self.status_var.set(message)

    def hide_progress(self):
        """隐藏进度条"""
        self.progress_frame.pack_forget()

    def update_progress(self, value, message=""):
        """更新进度"""
        self.progress_var.set(value)
        if message:
            self.status_var.set(message)
        self.root.update_idletasks()
        
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = Battlefield6Optimizer()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()
